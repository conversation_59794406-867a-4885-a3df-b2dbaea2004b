from rest_framework.test import APITestCase

from django.urls import reverse

from authentication.factories import UserFactory
from companies.models import CompanyType
from prefectures.factories import PrefecturesFactory
from users.factories import UserKannaFactory

from .factories import CompanyFactory


class CompanyAPITests(APITestCase):
    def test_get_company_list(self):
        CompanyFactory.create_batch(10, type=CompanyType.ACT)
        url = reverse("companies:companies-list", kwargs={"version": "v1"})
        response = self.client.get(url)
        data = response.json()
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(data["results"]), 10)

    def test_member_of_company(self):
        companies = CompanyFactory.create_batch(10)
        url = reverse(
            "companies:companies-members",
            kwargs={"version": "v1", "pk": companies[0].pk},
        )
        response = self.client.get(url)
        data = response.json()
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(data["results"]), 0)

        UserKannaFactory.create_batch(10, company_id=companies[0].pk)
        url = reverse(
            "companies:companies-members",
            kwargs={"version": "v1", "pk": companies[0].pk},
        )
        response = self.client.get(url)
        data = response.json()
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(data["results"]), 10)

    def test_get_users_for_company(self):
        prefectures = PrefecturesFactory()
        company = CompanyFactory.create(prefectures_id=prefectures.id)
        company2 = CompanyFactory.create(prefectures_id=prefectures.id)
        UserFactory.create_batch(19, company_id=company.pk)
        UserFactory.create_batch(2, company_id=company2.pk)
        url = reverse(
            "companies:companies-owners", kwargs={"version": "v1", "pk": company.pk}
        )
        response = self.client.get(url)
        data = response.json()
        self.assertEqual(data["count"], 19)
        self.assertEqual(len(data["results"]), 19)

        response = self.client.get(url + "?keyword=abc")
        data = response.json()
        self.assertEqual(data["count"], 19)
        self.assertEqual(len(data["results"]), 19)

        response = self.client.get(url + "?keyword=email")
        data = response.json()
        self.assertEqual(data["count"], 0)
        self.assertEqual(len(data["results"]), 0)

        response = self.client.get(url + "?is_active=false")
        data = response.json()
        self.assertEqual(data["count"], 0)
        self.assertEqual(len(data["results"]), 0)

        response = self.client.get(url + "?is_active=true")
        data = response.json()
        self.assertEqual(data["count"], 19)
        self.assertEqual(len(data["results"]), 19)

    def test_create_company_type_shop(self):
        prefectures = PrefecturesFactory()
        company = CompanyFactory.create(
            prefectures_id=prefectures.id, type=CompanyType.SHOP
        )
        self.assertEqual(company.control_id, 30000)
        companies = CompanyFactory.create_batch(
            30, prefectures_id=prefectures.id, type=CompanyType.SHOP
        )
        self.assertEqual(companies[0].control_id, 30001)
        self.assertEqual(companies[29].control_id, 30030)

    def test_create_company_type_act(self):
        prefectures = PrefecturesFactory()
        company = CompanyFactory.create(
            prefectures_id=prefectures.id, type=CompanyType.ACT
        )
        self.assertEqual(company.control_id, 10000)
        companies = CompanyFactory.create_batch(
            30, prefectures_id=prefectures.id, type=CompanyType.ACT
        )
        self.assertEqual(companies[0].control_id, 10001)
        self.assertEqual(companies[29].control_id, 10030)

    def test_create_company_type_dealer(self):
        prefectures = PrefecturesFactory()
        company = CompanyFactory.create(
            prefectures_id=prefectures.id, type=CompanyType.DEALER
        )
        self.assertEqual(company.control_id, 20000)
        companies = CompanyFactory.create_batch(
            30, prefectures_id=prefectures.id, type=CompanyType.DEALER
        )
        self.assertEqual(companies[0].control_id, 20001)
        self.assertEqual(companies[29].control_id, 20030)
